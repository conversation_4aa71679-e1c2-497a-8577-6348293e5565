"""
Bad Case 重试服务模块
处理用户点击badcase按钮后的自动重试机制
"""

import asyncio
import threading
from typing import Optional

from src.utils.logger import logger
from src.services.chatbot.bad_case_service import mark_bad_case_by_chat_history_id
from src.services.chatbot.history_service import save_user_message
from src.repositories.chatbi.history import get_chat_history_by_id
from src.services.user_query_service import UserQueryService


def handle_bad_case_with_retry(
    conversation_id: str,
    user_name: str,
    chat_history_id: int = None,
    open_message_id: str = None,
    **kwargs
) -> bool:
    """处理badcase标记并启动重试机制
    
    Args:
        conversation_id: 对话ID
        user_name: 用户名
        chat_history_id: 聊天历史ID
        open_message_id: 飞书原始消息ID
        **kwargs: 其他参数
        
    Returns:
        bool: 处理是否成功
    """
    try:
        logger.info(f"开始处理badcase重试 - user: {user_name}, conversation_id: {conversation_id}, chat_history_id: {chat_history_id}")
        
        # 1. 首先执行原有的badcase标记功能
        success = mark_bad_case_by_chat_history_id(
            chat_history_id=chat_history_id,
            is_bad_case=True,
            user_name=user_name
        )
        
        if not success:
            logger.error(f"标记badcase失败，无法继续重试流程")
            return False
            
        # 2. 获取用户信息用于写入重试消息
        if not chat_history_id:
            logger.error("缺少chat_history_id，无法获取用户信息")
            return False
            
        chat_record = get_chat_history_by_id(chat_history_id)
        if not chat_record:
            logger.error(f"无法找到chat_history记录: {chat_history_id}")
            return False
            
        user_email = chat_record.get('email')
        username = chat_record.get('username')
        
        if not user_email or not username:
            logger.error(f"chat_history记录缺少用户信息: {chat_record}")
            return False
            
        # 3. 写入重试提示消息到chat_history
        retry_message = "数据不对哦，请你仔细检查边界条件，充分考虑之后再试试。"
        
        message_saved = save_user_message(
            username=username,
            email=user_email,
            conversation_id=conversation_id,
            content=retry_message
        )
        
        if not message_saved:
            logger.error("保存重试消息失败")
            return False
            
        logger.info(f"已保存重试消息到chat_history: {retry_message}")
        
        # 4. 发送重试交互卡片
        if not open_message_id:
            logger.warning("缺少open_message_id，无法发送重试卡片")
            return True  # badcase标记已成功，只是无法发送卡片

        # 构建用户信息字典，获取真实的open_id
        user_info_dict = _get_real_user_info_from_chat_history_sync(chat_history_id, username)

        # 在后台线程中异步发送重试卡片
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(_send_retry_card(
                open_message_id, user_info_dict, conversation_id
            ))
        except RuntimeError:
            # 没有运行中的事件循环，在后台线程中运行
            threading.Thread(
                target=lambda: asyncio.run(
                    _send_retry_card(open_message_id, user_info_dict, conversation_id)
                ),
                daemon=True
            ).start()

        logger.info(f"badcase重试流程启动成功 - conversation_id: {conversation_id}")
        return True
        
    except Exception as e:
        logger.exception(f"处理badcase重试时出错: {e}")
        return False


async def _send_retry_card(
    open_message_id: str,
    user_info_dict: dict,
    conversation_id: str
):
    """发送重试交互卡片的异步函数

    Args:
        open_message_id: 飞书原始消息ID
        user_info_dict: 用户信息字典
        conversation_id: 对话ID
    """
    try:
        from src.services.feishu.message_apis import reply_simple_text_message

        logger.info(f"发送重试卡片 - open_message_id: {open_message_id}")

        # 发送简单的重试提示消息
        retry_card_message = (
            "数据不对哦，请你仔细检查边界条件，充分考虑之后再试试。\n\n"
            "💡 您可以：\n"
            "• 长按此消息回复，重新描述您的问题\n"
            "• 提供更多具体的条件和要求\n"
            "• 检查数据范围和时间条件是否正确"
        )

        reply_simple_text_message(open_message_id, retry_card_message)

        logger.info(f"重试卡片发送完成 - open_message_id: {open_message_id}")

    except Exception as e:
        logger.exception(f"发送重试卡片时出错: {e}")


def _get_real_user_info_from_chat_history_sync(chat_history_id: int, fallback_user_name: str) -> dict:
    """从chat_history同步获取真实的用户信息

    Args:
        chat_history_id: 聊天历史ID
        fallback_user_name: 备用用户名

    Returns:
        dict: 包含用户信息的字典
    """
    try:
        if not chat_history_id:
            return {
                'name': fallback_user_name,
                'email': f"{fallback_user_name}@unknown.com",
                'open_id': 'unknown_user'
            }

        chat_record = get_chat_history_by_id(chat_history_id)
        if chat_record:
            username = chat_record.get('username', fallback_user_name)
            email = chat_record.get('email', f"{fallback_user_name}@unknown.com")

            # 尝试从用户服务获取真实的open_id
            open_id = 'unknown_user'
            try:
                user_info = UserQueryService.get_user_info_by_email(email)
                if user_info and user_info.get('open_id'):
                    open_id = user_info['open_id']
                    logger.info(f"成功获取用户open_id: {email} -> {open_id}")
                else:
                    logger.warning(f"无法从用户服务获取open_id: {email}")
            except Exception as e:
                logger.warning(f"查询用户open_id失败: {email}, error: {e}")

            return {
                'name': username,
                'email': email,
                'open_id': open_id
            }
        else:
            logger.warning(f"无法找到chat_history记录: {chat_history_id}")
            return {
                'name': fallback_user_name,
                'email': f"{fallback_user_name}@unknown.com",
                'open_id': 'unknown_user'
            }

    except Exception as e:
        logger.exception(f"获取用户信息失败: {e}")
        return {
            'name': fallback_user_name,
            'email': f"{fallback_user_name}@unknown.com",
            'open_id': 'unknown_user'
        }
